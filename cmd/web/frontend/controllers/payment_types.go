package controllers

import (
	"github.com/gin-gonic/gin"
	"net/http"
	"yotracker/config"
	"yotracker/internal/models"
)

func GetAllPaymentTypes(c *gin.Context) {
	var paymentTypes []models.PaymentType
	filter := map[string]interface{}{}
	if active := c.Query("active"); active != "" {
		filter["active"] = active
	}
	if isOnline := c.Query("is_online"); isOnline != "" {
		filter["is_online"] = isOnline
	}
	config.DB.Where(filter).Order("id desc").Find(&paymentTypes)
	c.JSON(http.StatusOK, gin.H{
		"data": paymentTypes,
	})
}

func GetPaymentTypeById(c *gin.Context) {
	var paymentType models.PaymentType
	if err := config.DB.First(&paymentType, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Payment type not found",
		})
		return
	}
	c.<PERSON>(http.StatusOK, gin.H{
		"data": paymentType,
	})
}

func CreatePaymentType(c *gin.Context) {
	var req models.PaymentTypeRequest

	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	paymentType := models.PaymentType{
		Name:        req.Name,
		Description: req.Description,
		IsCash:      req.IsCash,
		Active:      req.Active,
		Options:     req.Options,
	}
	result := config.DB.Create(&paymentType)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Payment type created successfully",
		"data":    paymentType,
	})
}

func UpdatePaymentType(c *gin.Context) {
	var req models.PaymentTypeRequest
	var paymentType models.PaymentType
	if err := config.DB.First(&paymentType, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Payment type not found",
		})
		return
	}

	if err := c.BindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": err.Error(),
		})
		return
	}
	paymentType.Name = req.Name
	//paymentType.SystemName = req.SystemName
	paymentType.Description = req.Description
	paymentType.IsCash = req.IsCash
	//paymentType.IsOnline = req.IsOnline
	//paymentType.IsSystem = req.IsSystem
	paymentType.Active = req.Active
	paymentType.Position = req.Position
	paymentType.UniqueId = req.UniqueId
	paymentType.Logo = req.Logo
	paymentType.ReportColor = req.ReportColor
	paymentType.Options = req.Options
	result := config.DB.Save(&paymentType)
	if result.Error != nil {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Payment type updated successfully",
	})
}

func DeletePaymentType(c *gin.Context) {
	var paymentType models.PaymentType
	if err := config.DB.First(&paymentType, c.Param("id")).Error; err != nil {
		c.JSON(http.StatusNotFound, gin.H{
			"message": "Payment type not found",
		})
		return
	}
	if paymentType.IsSystem != nil && *paymentType.IsSystem {
		c.JSON(http.StatusBadRequest, gin.H{
			"message": "System payment type cannot be deleted",
		})
		return
	}

	result := config.DB.Delete(&paymentType)
	if result.Error != nil {
		c.JSON(http.StatusInternalServerError, gin.H{
			"message": result.Error.Error(),
		})
		return
	}

	c.JSON(http.StatusOK, gin.H{
		"message": "Payment type deleted successfully",
	})
}

func SearchPaymentTypes(c *gin.Context) {
	var paymentTypes []models.PaymentType
	filter := map[string]interface{}{}
	if active := c.Query("active"); active != "" {
		filter["active"] = active
	}
	if systemName := c.Query("system_name"); systemName != "" {
		filter["system_name"] = systemName
	}
	if isOnline := c.Query("is_online"); isOnline != "" {
		filter["is_online"] = isOnline
	}

	if name := c.Query("name"); name != "" {
		config.DB.Where(filter).Where("name LIKE ?", "%"+name+"%").Order("id desc").Find(&paymentTypes)
	} else {
		config.DB.Where(filter).Order("id desc").Find(&paymentTypes)
	}

	c.JSON(http.StatusOK, gin.H{
		"data": paymentTypes,
	})
}
