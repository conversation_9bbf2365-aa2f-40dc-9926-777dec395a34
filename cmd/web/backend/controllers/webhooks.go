package controllers

import (
	"fmt"
	"github.com/gin-gonic/gin"
	"time"
	"yotracker/config"
	"yotracker/internal/models"
)

func PaynowWebhook(c *gin.Context) {
	var paymentType models.PaymentType
	if err := config.DB.Where("system_name = ?", "paynow").First(&paymentType).Error; err != nil {
		c.JSON(400, gin.H{
			"message": "Payment type not found",
		})
		return
	}
	pollUrl := c.Request.FormValue("pollurl")
	status := c.Request.FormValue("status")
	amount := c.Request.FormValue("amount")
	hash := c.Request.FormValue("hash")
	reference := c.Request.FormValue("reference")
	paynowReference := c.Request.FormValue("paynowreference")
	//verify hash
	var invoice models.Invoice
	if err := config.DB.Preload("Client").Preload("Currency").Preload("InvoiceItems").Preload("InvoicePayments").Preload("InvoicePayments.PaymentType").Where("reference = ?", reference).First(&invoice).Error; err != nil {
		c.JSON(400, gin.H{
			"message": "Invoice not found",
		})
		return
	}
	if status == "Paid" || status == "Awaiting Delivery" {
		today := time.Now()
		payment := models.InvoicePayment{
			InvoiceId:     invoice.Id,
			PaymentTypeId: paymentType.Id,
			CurrencyId:    invoice.CurrencyId,
			Date:          today,
			TransId:       &paynowReference,
			Amount:        invoice.Amount,
			Xrate:         invoice.Xrate,
		}
		result := config.DB.Create(&payment)
		if result.Error != nil {
			c.JSON(400, gin.H{
				"message": "Failed to create payment",
			})
			return
		}

	}
	fmt.Println(pollUrl, status, amount, hash, reference, paynowReference)
	c.JSON(200, gin.H{
		"message": "Webhook received",
	})
}
